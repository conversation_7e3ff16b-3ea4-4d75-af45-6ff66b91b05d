"""
Flask REST API for MultipleFinger Bridge
Provides HTTP endpoints that communicate with C# TCP server
"""

import os
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv
from tcp_client import TcpClient, TcpClientError

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configuration
TCP_HOST = os.getenv('TCP_HOST', 'localhost')
TCP_PORT = int(os.getenv('TCP_PORT', '8123'))  # Updated default to 8123
API_PORT = int(os.getenv('API_PORT', '5001'))  # Default to 5001 if not set
DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'

# Force port 5001 to avoid conflicts
API_PORT = 5001

# Debug: Print configuration
print(f"Configuration loaded:")
print(f"  TCP_HOST: {TCP_HOST}")
print(f"  TCP_PORT: {TCP_PORT} (MainForm.cs TCP Bridge)")
print(f"  API_PORT: {API_PORT}")
print(f"  DEBUG: {DEBUG}")
print(f"  .env file path: {os.path.abspath('.env')}")
print(f"  Current working directory: {os.getcwd()}")

# Create TCP client instance
tcp_client = TcpClient(TCP_HOST, TCP_PORT)


# Simple validation functions (avoiding Marshmallow version issues)
def validate_capture_request(data):
    """Validate capture request data"""
    errors = {}

    # Required fields
    if 'finger_position' not in data:
        errors['finger_position'] = 'Required field'
    elif not isinstance(data['finger_position'], int) or not (1 <= data['finger_position'] <= 14):
        errors['finger_position'] = 'Must be an integer between 1 and 14 (1-10 for individual fingers, 11-14 for slaps)'

    # Optional fields with defaults
    data.setdefault('user_id', 'TEMP_USER')
    if not isinstance(data['user_id'], str) or not data['user_id'].strip():
        errors['user_id'] = 'Must be a non-empty string'

    data.setdefault('operation_type', 'flat')
    if data['operation_type'] not in ['flat', 'rolled', 'slaps']:
        errors['operation_type'] = 'Must be one of: flat, rolled, slaps'

    data.setdefault('timeout', 30)
    if not isinstance(data['timeout'], int) or not (1 <= data['timeout'] <= 300):
        errors['timeout'] = 'Must be an integer between 1 and 300'

    data.setdefault('save_image', True)
    if not isinstance(data['save_image'], bool):
        errors['save_image'] = 'Must be a boolean'

    return errors


def validate_identify_request(data):
    """Validate identify request data"""
    errors = {}

    # MainForm.cs IDENTIFY command doesn't use template_data or threshold
    # These are optional for compatibility
    data.setdefault('template_data', '')
    data.setdefault('threshold', 70)

    if 'threshold' in data and not isinstance(data['threshold'], int) or not (0 <= data['threshold'] <= 100):
        errors['threshold'] = 'Must be an integer between 0 and 100'

    return errors


def validate_verify_request(data):
    """Validate verify request data"""
    errors = {}

    if 'user_id' not in data:
        errors['user_id'] = 'Required field'
    elif not isinstance(data['user_id'], str) or not data['user_id'].strip():
        errors['user_id'] = 'Must be a non-empty string'

    if 'finger_position' not in data:
        errors['finger_position'] = 'Required field'
    elif not isinstance(data['finger_position'], int) or not (1 <= data['finger_position'] <= 10):
        errors['finger_position'] = 'Must be an integer between 1 and 10 (individual fingers only for verify)'

    return errors


def validate_enroll_request(data):
    """Validate enroll request data"""
    errors = {}

    if 'user_id' not in data:
        errors['user_id'] = 'Required field'
    elif not isinstance(data['user_id'], str) or not data['user_id'].strip():
        errors['user_id'] = 'Must be a non-empty string'

    if 'finger_position' not in data:
        errors['finger_position'] = 'Required field'
    elif not isinstance(data['finger_position'], int) or not (1 <= data['finger_position'] <= 14):
        errors['finger_position'] = 'Must be an integer between 1 and 14 (1-10 for individual fingers, 11-14 for slaps)'

    if 'template_data' not in data:
        errors['template_data'] = 'Required field'
    elif not isinstance(data['template_data'], str):
        errors['template_data'] = 'Must be a string'

    data.setdefault('image_data', '')
    data.setdefault('image_quality', 0)

    if not isinstance(data['image_quality'], int) or not (0 <= data['image_quality'] <= 100):
        errors['image_quality'] = 'Must be an integer between 0 and 100'

    return errors


# Error handlers
def handle_validation_error(errors):
    return jsonify({
        'success': False,
        'error': 'Validation error',
        'details': errors
    }), 400


@app.errorhandler(TcpClientError)
def handle_tcp_error(e):
    return jsonify({
        'success': False,
        'error': 'Communication error',
        'details': str(e)
    }), 503


@app.errorhandler(Exception)
def handle_general_error(e):
    logger.error(f"Unexpected error: {e}")
    return jsonify({
        'success': False,
        'error': 'Internal server error',
        'details': str(e) if DEBUG else 'An unexpected error occurred'
    }), 500


# API Routes
@app.route('/api/status', methods=['GET'])
def get_status():
    """Get server and device status"""
    try:
        status_data = tcp_client.get_status()
        return jsonify({
            'success': True,
            'data': status_data
        })
    except TcpClientError as e:
        raise e


@app.route('/api/fingerprint/capture', methods=['POST'])
def capture_fingerprint():
    """Initiate fingerprint capture"""
    try:
        # Get and validate request data
        data = request.json or {}
        errors = validate_capture_request(data)

        if errors:
            return handle_validation_error(errors)

        # Send capture command using MainForm.cs protocol
        result = tcp_client.capture_fingerprint(
            finger_position=data['finger_position'],
            user_id=data['user_id'],
            operation_type=data['operation_type'],
            timeout=data['timeout'],
            save_image=data['save_image']
        )

        return jsonify({
            'success': True,
            'data': result
        })

    except TcpClientError as e:
        raise e


@app.route('/api/fingerprint/identify', methods=['POST'])
def identify_fingerprint():
    """Identify a fingerprint template (1:N matching)"""
    try:
        # Get and validate request data
        data = request.json or {}
        errors = validate_identify_request(data)

        if errors:
            return handle_validation_error(errors)

        # Send identify command using MainForm.cs protocol
        result = tcp_client.identify_fingerprint(
            template_data=data.get('template_data'),
            threshold=data['threshold']
        )

        return jsonify({
            'success': True,
            'data': result
        })

    except TcpClientError as e:
        raise e


@app.route('/api/fingerprint/verify', methods=['POST'])
def verify_fingerprint():
    """Verify a fingerprint against specific user template (1:1 matching)"""
    try:
        # Get and validate request data
        data = request.json or {}
        errors = validate_verify_request(data)

        if errors:
            return handle_validation_error(errors)

        # Send verify command using MainForm.cs protocol
        result = tcp_client.verify_fingerprint(
            user_id=data['user_id'],
            finger_position=data['finger_position']
        )

        return jsonify({
            'success': True,
            'data': result
        })

    except TcpClientError as e:
        raise e


@app.route('/api/fingerprint/enroll', methods=['POST'])
def enroll_fingerprint():
    """Enroll a fingerprint template"""
    try:
        # Get and validate request data
        data = request.json or {}
        errors = validate_enroll_request(data)

        if errors:
            return handle_validation_error(errors)

        # Send enroll command using MainForm.cs protocol
        result = tcp_client.enroll_fingerprint(
            user_id=data['user_id'],
            finger_position=data.get('finger_position'),
            template_data=data.get('template_data', ''),
            image_data=data.get('image_data', ''),
            image_quality=data.get('image_quality', 0)
        )

        return jsonify({
            'success': True,
            'data': result
        })

    except TcpClientError as e:
        raise e


@app.route('/api/fingerprint/captured-data', methods=['GET'])
def get_captured_data():
    """Get captured fingerprint data from bridge application"""
    try:
        result = tcp_client.get_captured_data()
        return jsonify({
            'success': True,
            'data': result
        })
    except TcpClientError as e:
        raise e


@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        # Test TCP connection
        tcp_connected = tcp_client.test_connection()
        
        return jsonify({
            'success': True,
            'data': {
                'api_status': 'running',
                'tcp_connection': 'connected' if tcp_connected else 'disconnected',
                'tcp_host': TCP_HOST,
                'tcp_port': TCP_PORT
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Health check failed',
            'details': str(e)
        }), 503


@app.route('/api/fingerprint/device/open', methods=['POST'])
def open_device():
    """Open the fingerprint device"""
    try:
        result = tcp_client.open_device()
        return jsonify({
            'success': True,
            'data': result
        })
    except TcpClientError as e:
        raise e


@app.route('/api/fingerprint/device/close', methods=['POST'])
def close_device():
    """Close the fingerprint device"""
    try:
        result = tcp_client.close_device()
        return jsonify({
            'success': True,
            'data': result
        })
    except TcpClientError as e:
        raise e


@app.route('/api/fingerprint/device/info', methods=['GET'])
def get_device_info():
    """Get device information and status"""
    try:
        result = tcp_client.get_device_info()
        return jsonify({
            'success': True,
            'data': result
        })
    except TcpClientError as e:
        raise e


@app.route('/api/fingerprint/positions', methods=['GET'])
def get_finger_positions():
    """Get available finger positions"""
    positions = {
        1: "Right Thumb",
        2: "Right Index",
        3: "Right Middle",
        4: "Right Ring",
        5: "Right Little",
        6: "Left Thumb",
        7: "Left Index",
        8: "Left Middle",
        9: "Left Ring",
        10: "Left Little",
        11: "Two Thumbs",
        12: "Left Four Fingers",
        13: "Right Four Fingers",
        14: "Two Fingers"
    }

    return jsonify({
        'success': True,
        'data': {
            'positions': positions,
            'operation_types': ["flat", "rolled", "slaps"]
        }
    })


@app.route('/', methods=['GET'])
def index():
    """API information endpoint"""
    return jsonify({
        'name': 'MultipleFinger Bridge REST API',
        'version': '1.0.0',
        'description': 'REST API wrapper for MultipleFinger C# application',
        'endpoints': {
            'GET /api/status': 'Get server and device status',
            'POST /api/fingerprint/device/open': 'Open fingerprint device',
            'POST /api/fingerprint/device/close': 'Close fingerprint device',
            'GET /api/fingerprint/device/info': 'Get device information and status',
            'POST /api/fingerprint/capture': 'Initiate fingerprint capture',
            'POST /api/fingerprint/identify': 'Identify fingerprint template (1:N matching)',
            'POST /api/fingerprint/verify': 'Verify fingerprint against specific user (1:1 matching)',
            'POST /api/fingerprint/enroll': 'Enroll fingerprint template',
            'GET /api/fingerprint/captured-data': 'Get captured data',
            'GET /api/fingerprint/positions': 'Get finger positions',
            'GET /api/health': 'Health check'
        },
        'tcp_server': {
            'host': TCP_HOST,
            'port': TCP_PORT
        }
    })


if __name__ == '__main__':
    logger.info(f"Starting MultipleFinger Bridge REST API on port {API_PORT}")
    logger.info(f"TCP Server: {TCP_HOST}:{TCP_PORT}")
    
    app.run(
        host='0.0.0.0',
        port=API_PORT,
        debug=DEBUG
    )
