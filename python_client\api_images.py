"""
Flask REST API for fetching fingerprint images from database
Provides HTTP endpoints to retrieve stored fingerprint images
"""

import os
import logging
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import mysql.connector
from mysql.connector import Error
import base64
import io
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'trustfinger'),
    'port': int(os.getenv('DB_PORT', '3306'))
}

def get_db_connection():
    """Create and return a database connection"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Error as e:
        logger.error(f"Database connection error: {e}")
        return None

@app.route('/api/images/user/<user_id>', methods=['GET'])
def get_user_images(user_id):
    """Get all fingerprint images for a specific user"""
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({
                'success': False,
                'error': 'Database connection failed'
            }), 500

        cursor = connection.cursor(dictionary=True)
        
        query = """
        SELECT id, UserId, FingerPosition, FingerPositionName, ImageQuality, 
               CreatedTime, FingerImage 
        FROM enrollusers 
        WHERE UserId = %s 
        ORDER BY FingerPosition
        """
        
        cursor.execute(query, (user_id,))
        results = cursor.fetchall()
        
        # Convert binary image data to base64
        for result in results:
            if result['FingerImage']:
                result['FingerImage'] = base64.b64encode(result['FingerImage']).decode('utf-8')
            result['CreatedTime'] = result['CreatedTime'].isoformat() if result['CreatedTime'] else None
        
        cursor.close()
        connection.close()
        
        return jsonify({
            'success': True,
            'data': {
                'user_id': user_id,
                'fingerprints': results,
                'count': len(results)
            }
        })
        
    except Exception as e:
        logger.error(f"Error fetching user images: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/images/position/<int:position>', methods=['GET'])
def get_images_by_position(position):
    """Get all fingerprint images for a specific finger position"""
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({
                'success': False,
                'error': 'Database connection failed'
            }), 500

        cursor = connection.cursor(dictionary=True)
        
        query = """
        SELECT id, UserId, FingerPosition, FingerPositionName, ImageQuality, 
               CreatedTime, FingerImage 
        FROM enrollusers 
        WHERE FingerPosition = %s 
        ORDER BY UserId
        """
        
        cursor.execute(query, (position,))
        results = cursor.fetchall()
        
        # Convert binary image data to base64
        for result in results:
            if result['FingerImage']:
                result['FingerImage'] = base64.b64encode(result['FingerImage']).decode('utf-8')
            result['CreatedTime'] = result['CreatedTime'].isoformat() if result['CreatedTime'] else None
        
        cursor.close()
        connection.close()
        
        return jsonify({
            'success': True,
            'data': {
                'position': position,
                'fingerprints': results,
                'count': len(results)
            }
        })
        
    except Exception as e:
        logger.error(f"Error fetching images by position: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/images/latest/<user_id>', methods=['GET'])
def get_latest_images(user_id):
    """Get the latest fingerprint images for each finger position for a user"""
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({
                'success': False,
                'error': 'Database connection failed'
            }), 500

        cursor = connection.cursor(dictionary=True)
        
        query = """
        SELECT e1.id, e1.UserId, e1.FingerPosition, e1.FingerPositionName, 
               e1.ImageQuality, e1.CreatedTime, e1.FingerImage
        FROM enrollusers e1
        INNER JOIN (
            SELECT FingerPosition, MAX(CreatedTime) as MaxTime
            FROM enrollusers
            WHERE UserId = %s
            GROUP BY FingerPosition
        ) e2 ON e1.FingerPosition = e2.FingerPosition 
              AND e1.CreatedTime = e2.MaxTime
        WHERE e1.UserId = %s
        ORDER BY e1.FingerPosition
        """
        
        cursor.execute(query, (user_id, user_id))
        results = cursor.fetchall()
        
        # Convert binary image data to base64
        for result in results:
            if result['FingerImage']:
                result['FingerImage'] = base64.b64encode(result['FingerImage']).decode('utf-8')
            result['CreatedTime'] = result['CreatedTime'].isoformat() if result['CreatedTime'] else None
        
        cursor.close()
        connection.close()
        
        return jsonify({
            'success': True,
            'data': {
                'user_id': user_id,
                'fingerprints': results,
                'count': len(results)
            }
        })
        
    except Exception as e:
        logger.error(f"Error fetching latest images: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/images/<int:image_id>', methods=['GET'])
def get_image_by_id(image_id):
    """Get a specific fingerprint image by ID"""
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({
                'success': False,
                'error': 'Database connection failed'
            }), 500

        cursor = connection.cursor(dictionary=True)
        
        query = """
        SELECT id, UserId, FingerPosition, FingerPositionName, ImageQuality, 
               CreatedTime, FingerImage 
        FROM enrollusers 
        WHERE id = %s
        """
        
        cursor.execute(query, (image_id,))
        result = cursor.fetchone()
        
        if not result:
            return jsonify({
                'success': False,
                'error': 'Image not found'
            }), 404
        
        # Convert binary image data to base64
        if result['FingerImage']:
            result['FingerImage'] = base64.b64encode(result['FingerImage']).decode('utf-8')
        result['CreatedTime'] = result['CreatedTime'].isoformat() if result['CreatedTime'] else None
        
        cursor.close()
        connection.close()
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        logger.error(f"Error fetching image by ID: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/images/search', methods=['GET'])
def search_images():
    """Search fingerprint images with filters"""
    try:
        user_id = request.args.get('user_id')
        position = request.args.get('position', type=int)
        min_quality = request.args.get('min_quality', type=int)
        max_quality = request.args.get('max_quality', type=int)
        limit = request.args.get('limit', 50, type=int)
        
        connection = get_db_connection()
        if not connection:
            return jsonify({
                'success': False,
                'error': 'Database connection failed'
            }), 500

        cursor = connection.cursor(dictionary=True)
        
        # Build dynamic query
        conditions = []
        params = []
        
        if user_id:
            conditions.append("UserId = %s")
            params.append(user_id)
        
        if position:
            conditions.append("FingerPosition = %s")
            params.append(position)
        
        if min_quality is not None:
            conditions.append("ImageQuality >= %s")
            params.append(min_quality)
        
        if max_quality is not None:
            conditions.append("ImageQuality <= %s")
            params.append(max_quality)
        
        where_clause = ""
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        
        query = f"""
        SELECT id, UserId, FingerPosition, FingerPositionName, ImageQuality, 
               CreatedTime, FingerImage 
        FROM enrollusers 
        {where_clause}
        ORDER BY CreatedTime DESC
        LIMIT %s
        """
        
        params.append(limit)
        
        cursor.execute(query, tuple(params))
        results = cursor.fetchall()
        
        # Convert binary image data to base64
        for result in results:
            if result['FingerImage']:
                result['FingerImage'] = base64.b64encode(result['FingerImage']).decode('utf-8')
            result['CreatedTime'] = result['CreatedTime'].isoformat() if result['CreatedTime'] else None
        
        cursor.close()
        connection.close()
        
        return jsonify({
            'success': True,
            'data': {
                'fingerprints': results,
                'count': len(results),
                'filters': {
                    'user_id': user_id,
                    'position': position,
                    'min_quality': min_quality,
                    'max_quality': max_quality
                }
            }
        })
        
    except Exception as e:
        logger.error(f"Error searching images: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/images/stats', methods=['GET'])
def get_image_stats():
    """Get statistics about stored fingerprint images"""
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({
                'success': False,
                'error': 'Database connection failed'
            }), 500

        cursor = connection.cursor(dictionary=True)
        
        # Get total count
        cursor.execute("SELECT COUNT(*) as total FROM enrollusers")
        total = cursor.fetchone()['total']
        
        # Get count by position
        cursor.execute("""
        SELECT FingerPosition, FingerPositionName, COUNT(*) as count 
        FROM enrollusers 
        GROUP BY FingerPosition, FingerPositionName 
        ORDER BY FingerPosition
        """)
        by_position = cursor.fetchall()
        
        # Get count by user
        cursor.execute("""
        SELECT UserId, COUNT(*) as count 
        FROM enrollusers 
        GROUP BY UserId 
        ORDER BY count DESC
        """)
        by_user = cursor.fetchall()
        
        # Get quality statistics
        cursor.execute("""
        SELECT 
            MIN(ImageQuality) as min_quality,
            MAX(ImageQuality) as max_quality,
            AVG(ImageQuality) as avg_quality,
            COUNT(CASE WHEN ImageQuality >= 80 THEN 1 END) as high_quality,
            COUNT(CASE WHEN ImageQuality < 50 THEN 1 END) as low_quality
        FROM enrollusers 
        WHERE ImageQuality IS NOT NULL
        """)
        quality_stats = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        return jsonify({
            'success': True,
            'data': {
                'total_images': total,
                'by_position': by_position,
                'by_user': by_user[:10],  # Top 10 users
                'quality_stats': quality_stats
            }
        })
        
    except Exception as e:
        logger.error(f"Error fetching image stats: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        connection = get_db_connection()
        if connection:
            connection.close()
            return jsonify({
                'success': True,
                'status': 'healthy',
                'database': 'connected'
            })
        else:
            return jsonify({
                'success': False,
                'status': 'unhealthy',
                'database': 'disconnected'
            }), 503
    except Exception as e:
        return jsonify({
            'success': False,
            'status': 'unhealthy',
            'error': str(e)
        }), 500

if __name__ == '__main__':
    logger.info("Starting Fingerprint Images API on port 5002")
    app.run(host='0.0.0.0', port=5002, debug=True)
