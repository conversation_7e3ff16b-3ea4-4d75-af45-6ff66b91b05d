@echo off
echo ========================================
echo Starting Simplified Fingerprint System
echo ========================================

REM Start the C# Bridge Application
echo Starting C# Bridge Application...
start "" "bin\Debug\MultipleFingerDemo.exe"

timeout /t 3 /nobreak > nul

REM Start Python Fingerprint API
echo Starting Python Fingerprint API...
cd python_client
start cmd /k "python api.py"

timeout /t 2 /nobreak > nul

REM Start Python Images API
echo Starting Python Images API...
start cmd /k "python api_images.py"

timeout /t 2 /nobreak > nul

echo.
echo ========================================
echo System Started Successfully!
echo ========================================
echo Services:
echo - C# Bridge: TCP Port 8123
echo - Fingerprint API: http://localhost:5001
echo - Images API: http://localhost:5002
echo.
echo Open web_demo/index.html in your browser
echo ========================================
pause
