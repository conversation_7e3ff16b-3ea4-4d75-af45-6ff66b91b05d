<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Aratek TrustFingerM Demo - Simplified</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .tab {
            display: inline-block;
            padding: 10px 30px;
            margin-right: 5px;
            border-radius: 8px 8px 0 0;
            background: #e0e0e0;
            color: #333;
            cursor: pointer;
            font-weight: bold;
            transition: background 0.2s, color 0.2s;
        }
        .tab.active {
            background: #1976d2;
            color: #fff;
        }
        .tab-content {
            display: none;
            padding: 20px 0 0 0;
        }
        .tab-content.active {
            display: block;
        }
        .finger-checkboxes {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;
        }
        .finger-checkboxes label {
            min-width: 100px;
            display: flex;
            align-items: center;
        }
        .preview-row {
            display: flex;
            gap: 20px;
            margin: 10px 0;
        }
        .preview-col {
            flex: 1;
            text-align: center;
        }
        .nfiq-label {
            font-size: 12px;
            color: #555;
            margin-top: 4px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
        }
        button:disabled {
            background-color: #cccccc;
            color: #888;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input[type="text"] {
            padding: 8px;
            width: 100%;
            max-width: 300px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .device-info {
            margin-bottom: 10px;
            font-size: 14px;
            color: #333;
        }
        .tip-label {
            font-size: 14px;
            color: #007bff;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Aratek TrustFingerM Demo - Simplified</h1>
        <div class="form-group">
            <label for="userId">Prisoner ID:</label>
            <input type="text" id="userId" placeholder="e.g. PRISONER_001" value="USER001" required />
        </div>
        <div class="controls">
            <button id="openDeviceBtn">Open Device</button>
            <button id="closeDeviceBtn">Close Device</button>
            <button id="deviceInfoBtn">Device Info</button>
        </div>
        <div class="device-info" id="deviceInfo"></div>
        <div class="tip-label" id="tipLabel">Welcome</div>
        
        <div style="margin: 20px 0">
            <div class="tab active" id="tab-slaps" onclick="switchTab('slaps')">Slaps</div>
            <div class="tab" id="tab-flat" onclick="switchTab('flat')">Flat Fingerprints</div>
            <div class="tab" id="tab-rolled" onclick="switchTab('rolled')">Rolled Fingerprints</div>
        </div>
        
        <div id="tab-content-slaps" class="tab-content active">
            <div class="finger-checkboxes" id="slapsFingerCheckboxes">
                <label>
                    <input type="checkbox" id="slaps_left4" />
                    Left Four Fingers
                </label>
                <label>
                    <input type="checkbox" id="slaps_right4" />
                    Right Four Fingers
                </label>
                <label>
                    <input type="checkbox" id="slaps_thumbs" />
                    Two Thumbs
                </label>
            </div>
            <div class="controls">
                <button id="captureBtn">Capture</button>
                <button id="enrollBtn">Enroll</button>
                <button id="identifyBtn">Identify</button>
            </div>
            <div class="preview-row">
                <div class="preview-col">
                    <h4>Left Four Fingers</h4>
                    <img id="leftFourPreview" style="max-width: 200px; display: none" />
                    <div class="nfiq-label" id="nfiqLeftFour">NFIQ=N/A</div>
                </div>
                <div class="preview-col">
                    <h4>Two Thumbs</h4>
                    <img id="twoThumbsPreview" style="max-width: 200px; display: none" />
                    <div class="nfiq-label" id="nfiqTwoThumbs">NFIQ=N/A</div>
                </div>
                <div class="preview-col">
                    <h4>Right Four Fingers</h4>
                    <img id="rightFourPreview" style="max-width: 200px; display: none" />
                    <div class="nfiq-label" id="nfiqRightFour">NFIQ=N/A</div>
                </div>
            </div>
        </div>
        
        <div id="tab-content-flat" class="tab-content">
            <div class="finger-checkboxes" id="flatFingerCheckboxes"></div>
            <div class="controls">
                <button id="captureFlatBtn">Capture</button>
                <button id="enrollFlatBtn">Enroll</button>
                <button id="identifyFlatBtn">Identify</button>
            </div>
            <div class="preview-row" id="flatPreviews"></div>
        </div>
        
        <div id="tab-content-rolled" class="tab-content">
            <div class="finger-checkboxes" id="rolledFingerCheckboxes"></div>
            <div class="controls">
                <button id="captureRolledBtn">Capture</button>
                <button id="enrollRolledBtn">Enroll</button>
                <button id="identifyRolledBtn">Identify</button>
            </div>
            <div class="preview-row" id="rolledPreviews"></div>
        </div>
        
        <div class="status" id="status"></div>
    </div>

    <script src="app_simplified.js"></script>
</body>
</html>
