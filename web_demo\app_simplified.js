// Simplified version for database-first approach
// Uses the same structure as original app.js but with database integration

// API endpoints - adjust these based on your deployment
const API_BASE_URL = "http://localhost:5001/api/fingerprint";
const IMAGES_API_URL = "http://localhost:5002/api/images";

console.log("Simplified Web Demo starting...");
console.log("Fingerprint API:", API_BASE_URL);
console.log("Images API:", IMAGES_API_URL);

// --- API Helper ---
async function apiRequest(endpoint, method = "GET", data = null, baseUrl = API_BASE_URL) {
  const options = { method, headers: { "Content-Type": "application/json" } };
  if (data) options.body = JSON.stringify(data);
  const res = await fetch(`${baseUrl}/${endpoint}`, options);
  if (!res.ok) throw new Error(await res.text());
  return await res.json();
}

// --- Tab and UI State ---
let currentTab = "slaps";
let selectedSlapsFingers = new Set();
let selectedRolledFinger = null;
let capturedTemplates = {};
let deviceOpen = false;

// --- MainForm Finger Mapping ---
const slapsFingers = [
  { id: "L1", label: "Left Thumb" },
  { id: "L2", label: "Left Index" },
  { id: "L3", label: "Left Middle" },
  { id: "L4", label: "Left Ring" },
  { id: "L5", label: "Left Little" },
  { id: "R1", label: "Right Thumb" },
  { id: "R2", label: "Right Index" },
  { id: "R3", label: "Right Middle" },
  { id: "R4", label: "Right Ring" },
  { id: "R5", label: "Right Little" },
];
const rolledFingers = slapsFingers;

// --- Tab Switching ---
function switchTab(tab) {
  currentTab = tab;
  
  // Remove 'active' from all tabs and tab-contents
  document.getElementById("tab-slaps").classList.remove("active");
  document.getElementById("tab-flat").classList.remove("active");
  document.getElementById("tab-rolled").classList.remove("active");
  document.getElementById("tab-content-slaps").classList.remove("active");
  document.getElementById("tab-content-flat").classList.remove("active");
  document.getElementById("tab-content-rolled").classList.remove("active");
  
  // Add 'active' to the selected tab and content
  document.getElementById("tab-" + tab).classList.add("active");
  document.getElementById("tab-content-" + tab).classList.add("active");
  
  renderFingerCheckboxes();
  updateButtonStates();
}

// --- Utility Functions ---
function setTip(msg, isError) {
  const tipLabel = document.getElementById("tipLabel");
  if (tipLabel) {
    tipLabel.textContent = msg;
    tipLabel.style.color = isError ? "#b00" : "#007bff";
  }
}

function updateButtonStates() {
  const captureBtn = document.getElementById("captureBtn");
  const enrollBtn = document.getElementById("enrollBtn");
  const identifyBtn = document.getElementById("identifyBtn");
  const captureRolledBtn = document.getElementById("captureRolledBtn");
  const enrollRolledBtn = document.getElementById("enrollRolledBtn");
  const identifyRolledBtn = document.getElementById("identifyRolledBtn");
  const captureFlatBtn = document.getElementById("captureFlatBtn");
  const enrollFlatBtn = document.getElementById("enrollFlatBtn");
  const identifyFlatBtn = document.getElementById("identifyFlatBtn");

  const left4 = document.getElementById("slaps_left4");
  const right4 = document.getElementById("slaps_right4");
  const thumbs = document.getElementById("slaps_thumbs");
  const slapsSelected = (left4 && left4.checked) || (right4 && right4.checked) || (thumbs && thumbs.checked);

  let activeTabId = 'slaps';
  if (document.getElementById('tab-content-slaps').classList.contains('active')) {
    activeTabId = 'slaps';
  } else if (document.getElementById('tab-content-flat').classList.contains('active')) {
    activeTabId = 'flat';
  } else if (document.getElementById('tab-content-rolled').classList.contains('active')) {
    activeTabId = 'rolled';
  }

  let hasValidSelection = false;
  if (activeTabId === 'slaps') {
    hasValidSelection = slapsSelected;
  } else if (activeTabId === 'rolled') {
    hasValidSelection = !!selectedRolledFinger;
  } else if (activeTabId === 'flat') {
    hasValidSelection = !!window.selectedFlatFinger;
  }

  if (captureBtn) captureBtn.disabled = !deviceOpen || !hasValidSelection;
  if (enrollBtn) enrollBtn.disabled = !deviceOpen || !slapsSelected;
  if (identifyBtn) identifyBtn.disabled = !deviceOpen || !slapsSelected;
  if (captureRolledBtn) captureRolledBtn.disabled = !deviceOpen || !selectedRolledFinger;
  if (enrollRolledBtn) enrollRolledBtn.disabled = !deviceOpen || !selectedRolledFinger;
  if (identifyRolledBtn) identifyRolledBtn.disabled = !deviceOpen || !selectedRolledFinger;
  if (captureFlatBtn) captureFlatBtn.disabled = !deviceOpen || !window.selectedFlatFinger;
  if (enrollFlatBtn) enrollFlatBtn.disabled = !deviceOpen || !window.selectedFlatFinger;
  if (identifyFlatBtn) identifyFlatBtn.disabled = !deviceOpen || !window.selectedFlatFinger;
}

// --- Render Finger Checkboxes ---
function updateSelectedSlapsFingers() {
  selectedSlapsFingers.clear();
  const left4 = document.getElementById("slaps_left4");
  const right4 = document.getElementById("slaps_right4");
  const thumbs = document.getElementById("slaps_thumbs");
  if (left4 && left4.checked) {
    ["L2", "L3", "L4", "L5"].forEach((f) => selectedSlapsFingers.add(f));
  }
  if (right4 && right4.checked) {
    ["R2", "R3", "R4", "R5"].forEach((f) => selectedSlapsFingers.add(f));
  }
  if (thumbs && thumbs.checked) {
    ["L1", "R1"].forEach((f) => selectedSlapsFingers.add(f));
  }
}

function renderFingerCheckboxes() {
  if (currentTab === "slaps") {
    const left4 = document.getElementById("slaps_left4");
    const right4 = document.getElementById("slaps_right4");
    const thumbs = document.getElementById("slaps_thumbs");
    
    if (left4 && !left4._listenerAdded) {
      left4.addEventListener("change", function () {
        updateSelectedSlapsFingers();
        updateButtonStates();
      });
      left4._listenerAdded = true;
    }
    if (right4 && !right4._listenerAdded) {
      right4.addEventListener("change", function () {
        updateSelectedSlapsFingers();
        updateButtonStates();
      });
      right4._listenerAdded = true;
    }
    if (thumbs && !thumbs._listenerAdded) {
      thumbs.addEventListener("change", function () {
        updateSelectedSlapsFingers();
        updateButtonStates();
      });
      thumbs._listenerAdded = true;
    }
    updateSelectedSlapsFingers();
  } else if (currentTab === "flat") {
    const flatBox = document.getElementById("flatFingerCheckboxes");
    if (!flatBox) return;
    flatBox.innerHTML = "";
    rolledFingers.forEach((f) => {
      const label = document.createElement("label");
      const cb = document.createElement("input");
      cb.type = "checkbox";
      cb.id = `flat_${f.id}`;
      cb.addEventListener("change", () => {
        if (cb.checked) {
          rolledFingers.forEach((ff) => {
            if (ff.id !== f.id) {
              const other = document.getElementById(`flat_${ff.id}`);
              if (other) other.checked = false;
            }
          });
          window.selectedFlatFinger = f.id;
        } else {
          window.selectedFlatFinger = null;
        }
        updateButtonStates();
      });
      label.appendChild(cb);
      label.appendChild(document.createTextNode(" " + f.label));
      flatBox.appendChild(label);
    });
  } else if (currentTab === "rolled") {
    const rolledBox = document.getElementById("rolledFingerCheckboxes");
    if (!rolledBox) return;
    rolledBox.innerHTML = "";
    rolledFingers.forEach((f) => {
      const label = document.createElement("label");
      const cb = document.createElement("input");
      cb.type = "checkbox";
      cb.id = `rolled_${f.id}`;
      cb.addEventListener("change", () => {
        if (cb.checked) {
          rolledFingers.forEach((ff) => {
            if (ff.id !== f.id) {
              const other = document.getElementById(`rolled_${ff.id}`);
              if (other) other.checked = false;
            }
          });
          selectedRolledFinger = f.id;
        } else {
          selectedRolledFinger = null;
        }
        updateButtonStates();
      });
      label.appendChild(cb);
      label.appendChild(document.createTextNode(" " + f.label));
      rolledBox.appendChild(label);
    });
  }
}

// --- Device Controls ---
document.getElementById("openDeviceBtn").addEventListener("click", async function () {
  try {
    setTip("Opening device...");
    const result = await apiRequest("device/open", "POST");
    deviceOpen = true;
    setTip("✅ Device opened successfully");
    updateButtonStates();
  } catch (e) {
    setTip("❌ Failed to open device: " + e.message, true);
  }
});

document.getElementById("closeDeviceBtn").addEventListener("click", async function () {
  try {
    setTip("Closing device...");
    await apiRequest("device/close", "POST");
    deviceOpen = false;
    setTip("✅ Device closed successfully");
    updateButtonStates();
  } catch (e) {
    setTip("❌ Failed to close device: " + e.message, true);
  }
});

document.getElementById("deviceInfoBtn").addEventListener("click", async function () {
  try {
    const info = await apiRequest("device/info", "GET");
    document.getElementById("deviceInfo").textContent = 
      `SN: ${info.data?.SERIAL_NUMBER || 'N/A'}, Model: ${info.data?.DEVICE_NAME || 'N/A'}, Status: ${info.data?.STATUS || 'Unknown'}`;
  } catch (e) {
    document.getElementById("deviceInfo").textContent = "Device info unavailable";
  }
});

// --- Capture/Enroll/Identify Buttons ---
document.getElementById("captureBtn").addEventListener("click", async function () {
  if (!deviceOpen) {
    setTip("❌ Please open device first", true);
    return;
  }

  const userId = document.getElementById("userId").value.trim();
  if (!userId) {
    setTip("❌ Please enter a Prisoner ID", true);
    return;
  }

  const left4 = document.getElementById("slaps_left4");
  const right4 = document.getElementById("slaps_right4");
  const thumbs = document.getElementById("slaps_thumbs");
  
  let positions = [];
  if (left4 && left4.checked) positions.push(12); // Left 4 fingers
  if (right4 && right4.checked) positions.push(13); // Right 4 fingers
  if (thumbs && thumbs.checked) positions.push(11); // Two thumbs

  if (positions.length === 0) {
    setTip("❌ Please select at least one slaps type", true);
    return;
  }

  try {
    setTip("Capturing fingerprints...");
    
    for (const position of positions) {
      const response = await apiRequest("capture", "POST", {
        user_id: userId,
        finger_position: position,
        operation_type: "slaps"
      });
      
      if (response.success) {
        setTip(`✅ Captured ${positionToName(position)} for ${userId}`);
      } else {
        setTip(`❌ Failed to capture ${positionToName(position)}`, true);
      }
    }
    
    setTip("✅ All captures completed successfully");
  } catch (e) {
    setTip("❌ Capture error: " + e.message, true);
  }
});

document.getElementById("enrollBtn").addEventListener("click", async function () {
  if (!deviceOpen) {
    setTip("❌ Please open device first", true);
    return;
  }

  const userId = document.getElementById("userId").value.trim();
  if (!userId) {
    setTip("❌ Please enter a Prisoner ID", true);
    return;
  }

  try {
    setTip("Enrolling fingerprints...");
    const result = await apiRequest("enroll", "POST", { user_id: userId });
    setTip(`✅ Enrolled fingerprints for ${userId}`);
  } catch (e) {
    setTip("❌ Enroll error: " + e.message, true);
  }
});

document.getElementById("identifyBtn").addEventListener("click", async function () {
  if (!deviceOpen) {
    setTip("❌ Please open device first", true);
    return;
  }

  try {
    setTip("Identifying...");
    const result = await apiRequest("identify", "POST");
    if (result.success && result.data && result.data.matches && result.data.matches.length > 0) {
      const match = result.data.matches[0];
      setTip(`✅ Identified: ${match.UserId || match.userId} (${match.FingerPositionName || match.fingerPositionName}, Score: ${match.Score || match.score})`);
    } else {
      setTip("❌ No match found");
    }
  } catch (e) {
    setTip("❌ Identify error: " + e.message, true);
  }
});

// --- Flat and Rolled Buttons ---
document.getElementById("captureFlatBtn").addEventListener("click", async function () {
  if (!deviceOpen || !window.selectedFlatFinger) {
    setTip("❌ Please open device and select a finger", true);
    return;
  }

  const userId = document.getElementById("userId").value.trim();
  if (!userId) {
    setTip("❌ Please enter a Prisoner ID", true);
    return;
  }

  try {
    setTip("Capturing flat fingerprint...");
    const response = await apiRequest("capture", "POST", {
      user_id: userId,
      finger_position: fingerIdToPosition(window.selectedFlatFinger),
      operation_type: "flat"
    });
    
    if (response.success) {
      setTip(`✅ Captured ${window.selectedFlatFinger} for ${userId}`);
    }
  } catch (e) {
    setTip("❌ Capture error: " + e.message, true);
  }
});

document.getElementById("captureRolledBtn").addEventListener("click", async function () {
  if (!deviceOpen || !selectedRolledFinger) {
    setTip("❌ Please open device and select a finger", true);
    return;
  }

  const userId = document.getElementById("userId").value.trim();
  if (!userId) {
    setTip("❌ Please enter a Prisoner ID", true);
    return;
  }

  try {
    setTip("Capturing rolled fingerprint...");
    const response = await apiRequest("capture", "POST", {
      user_id: userId,
      finger_position: fingerIdToPosition(selectedRolledFinger),
      operation_type: "rolled"
    });
    
    if (response.success) {
      setTip(`✅ Captured ${selectedRolledFinger} for ${userId}`);
    }
  } catch (e) {
    setTip("❌ Capture error: " + e.message, true);
  }
});

// --- Helper Functions ---
function fingerIdToPosition(fid) {
  const map = {
    L1: 6, L2: 7, L3: 8, L4: 9, L5: 10,
    R1: 1, R2: 2, R3: 3, R4: 4, R5: 5,
  };
  return map[fid] || 1;
}

function positionToName(position) {
  const names = {
    11: "Two Thumbs",
    12: "Left Four Fingers",
    13: "Right Four Fingers",
    1: "Right Thumb", 2: "Right Index", 3: "Right Middle", 4: "Right Ring", 5: "Right Little",
    6: "Left Thumb", 7: "Left Index", 8: "Left Middle", 9: "Left Ring", 10: "Left Little"
  };
  return names[position] || `Position ${position}`;
}

// --- Initialization ---
document.addEventListener("DOMContentLoaded", function () {
  renderFingerCheckboxes();
  updateButtonStates();
});
